<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Format language settings
return [
    'invalidFormatter' => '"{0}" is not a valid Formatter class.',
    'invalidJSON'      => 'Failed to parse JSON string. Error: {0}',
    'invalidMime'      => 'No Formatter defined for mime type: "{0}".',
    'missingExtension' => 'The SimpleXML extension is required to format XML.',
];
