{"name": "codeigniter4/codeigniter4", "description": "The CodeIgniter framework v4", "license": "MIT", "type": "project", "homepage": "https://codeigniter.com", "support": {"forum": "https://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}, "require": {"php": "^8.1", "ext-intl": "*", "ext-mbstring": "*", "laminas/laminas-escaper": "^2.14", "psr/log": "^3.0"}, "require-dev": {"codeigniter/phpstan-codeigniter": "1.x-dev", "fakerphp/faker": "^1.24", "kint-php/kint": "^6.0", "mikey179/vfsstream": "^1.6.12", "nexusphp/tachycardia": "^2.0", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpcov": "^9.0.2 || ^10.0", "phpunit/phpunit": "^10.5.16 || ^11.2", "predis/predis": "^1.1 || ^2.3", "rector/rector": "2.0.14", "shipmonk/phpstan-baseline-per-identifier": "^2.0"}, "replace": {"codeigniter4/framework": "self.version"}, "suggest": {"ext-curl": "If you use CURLRequest class", "ext-dom": "If you use TestResponse", "ext-exif": "If you run Image class tests", "ext-fileinfo": "Improves mime type detection for files", "ext-gd": "If you use Image class GDHandler", "ext-imagick": "If you use Image class ImageMagickHandler", "ext-libxml": "If you use TestResponse", "ext-memcache": "If you use Cache class MemcachedHandler with Memcache", "ext-memcached": "If you use Cache class MemcachedHandler with Memcached", "ext-mysqli": "If you use MySQL", "ext-oci8": "If you use Oracle Database", "ext-pgsql": "If you use PostgreSQL", "ext-readline": "Improves CLI::input() usability", "ext-redis": "If you use Cache class RedisHandler", "ext-simplexml": "If you format XML", "ext-sodium": "If you use Encryption SodiumHandler", "ext-sqlite3": "If you use SQLite3", "ext-sqlsrv": "If you use SQL Server", "ext-xdebug": "If you use CIUnitTestCase::assertHeaderEmitted()"}, "autoload": {"psr-4": {"CodeIgniter\\": "system/"}, "exclude-from-classmap": ["**/Database/Migrations/**", "system/ThirdParty/**", "tests/system/Config/fixtures/**"]}, "autoload-dev": {"psr-4": {"CodeIgniter\\": "tests/system/", "Utils\\": "utils/src/"}}, "config": {"allow-plugins": {"phpstan/extension-installer": true}, "optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"branch-alias": {"dev-*": "4.6.x-dev"}}, "scripts": {"post-update-cmd": ["CodeIgniter\\ComposerScripts::postUpdate"], "post-autoload-dump": ["@composer update --working-dir=utils --ignore-platform-req=php"], "analyze": ["Composer\\Config::disableProcessTimeout", "@phpstan:check", "vendor/bin/rector process --dry-run"], "cs": ["Composer\\Config::disableProcessTimeout", "utils/vendor/bin/php-cs-fixer check --ansi --verbose --diff --config=.php-cs-fixer.user-guide.php", "utils/vendor/bin/php-cs-fixer check --ansi --verbose --diff --config=.php-cs-fixer.no-header.php", "utils/vendor/bin/php-cs-fixer check --ansi --verbose --diff --config=.php-cs-fixer.tests.php", "utils/vendor/bin/php-cs-fixer check --ansi --verbose --diff"], "cs-fix": ["Composer\\Config::disableProcessTimeout", "utils/vendor/bin/php-cs-fixer fix --ansi --verbose --diff --config=.php-cs-fixer.user-guide.php", "utils/vendor/bin/php-cs-fixer fix --ansi --verbose --diff --config=.php-cs-fixer.no-header.php", "utils/vendor/bin/php-cs-fixer fix --ansi --verbose --diff --config=.php-cs-fixer.tests.php", "utils/vendor/bin/php-cs-fixer fix --ansi --verbose --diff"], "metrics": "utils/vendor/bin/phpmetrics --config=phpmetrics.json", "phpstan:baseline": ["bash -c \"rm -rf utils/phpstan-baseline/*.neon\"", "bash -c \"touch utils/phpstan-baseline/loader.neon\"", "phpstan analyse --ansi --generate-baseline=utils/phpstan-baseline/loader.neon", "split-phpstan-baseline utils/phpstan-baseline/loader.neon"], "phpstan:check": "vendor/bin/phpstan analyse --verbose --ansi", "sa": "@analyze", "style": "@cs-fix", "test": "phpunit"}, "scripts-descriptions": {"analyze": "Run static analysis", "cs": "Check the coding style", "cs-fix": "Fix the coding style", "metrics": "Run PhpMetrics", "phpstan:baseline": "Run PHPStan then dump all errors to baseline", "phpstan:check": "Run PHPStan with support for identifiers", "test": "Run unit tests"}}