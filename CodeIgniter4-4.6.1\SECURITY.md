# Security Policy

The development team and community take all security issues seriously. **Please do not make public any uncovered flaws.**

## Reporting a Vulnerability

Thank you for improving the security of our code! Any assistance in removing security flaws will be acknowledged.

**Please report security flaws by emailing the development team directly: <EMAIL>**.

The lead maintainer will acknowledge your email within 48 hours, and will send a more detailed response within 48 hours indicating
the next steps in handling your report. After the initial reply to your report, the security team will endeavor to keep you informed of the
progress towards a fix and full announcement, and may ask for additional information or guidance.

## Disclosure Policy

When the security team receives a security bug report, they will assign it to a primary handler.
This person will coordinate the fix and release process, involving the following steps:

- Confirm the problem and determine the affected versions.
- Audit code to find any potential similar problems.
- Prepare fixes for all releases still under maintenance. These fixes will be released as fast as possible.
- Publish security advisories at https://github.com/codeigniter4/CodeIgniter4/security/advisories

## Comments on this Policy

If you have suggestions on how this process could be improved please submit a Pull Request.
